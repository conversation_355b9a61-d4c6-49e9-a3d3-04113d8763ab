/**
 * 管理员卡密/激活码管理模块
 * 提供卡密生成、管理、统计等功能
 */

const crypto = require('crypto');

// 管理员卡密管理模块设置函数
async function setupAdminActivationCodes(app, io, coreData, authData) {
  console.log('🔧 设置管理员卡密管理模块...');
  
  const { 
    pool,
    devices, 
    webClients,
    logs,
    pendingCommands,
    deviceCommands,
    throttledLog
  } = coreData;

  const { authenticateToken } = authData;

  // 引入认证服务和数据库连接
  const { localPool, mainPool } = require('../config/database');
  const AuthService = require('../services/AuthService');
  
  // 创建认证服务实例
  const authService = new AuthService(mainPool, localPool);

  // 管理员权限验证中间件
  const requireAdmin = (req, res, next) => {
    if (!req.user || req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: '需要管理员权限'
      });
    }
    next();
  };

  // 生成卡密代码 - 32位，根据天数生成前缀
  function generateActivationCode(durationDays) {
    // 根据天数确定前缀
    let prefix = '';
    if (durationDays <= 7) {
      prefix = '7DAY';
    } else if (durationDays <= 30) {
      prefix = 'MONTH';
    } else if (durationDays <= 180) {
      prefix = 'HALF';
    } else if (durationDays <= 365) {
      prefix = 'YEAR';
    } else {
      prefix = 'LONG';
    }

    // 生成随机字符串，确保总长度为32位
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    const prefixLength = prefix.length;
    const randomLength = 32 - prefixLength;

    let randomPart = '';
    for (let i = 0; i < randomLength; i++) {
      randomPart += chars.charAt(Math.floor(Math.random() * chars.length));
    }

    return prefix + randomPart;
  }

  // 批量生成卡密API
  app.post('/api/admin/activation-codes/generate', authenticateToken, requireAdmin, async (req, res) => {
    try {
      const {
        count = 1,
        type = 'card',
        durationDays = 30,
        maxUses = 1,
        description = '',
        batchId = '',
        expiresAt = null
      } = req.body;

      console.log('管理员生成卡密:', { count, type, durationDays, maxUses });

      // 参数验证
      if (!count || count < 1 || count > 1000) {
        return res.status(400).json({
          success: false,
          message: '生成数量必须在1-1000之间'
        });
      }

      if (!durationDays || durationDays < 1 || durationDays > 3650) {
        return res.status(400).json({
          success: false,
          message: '时效天数必须在1-3650之间'
        });
      }

      if (!type) {
        return res.status(400).json({
          success: false,
          message: '请选择卡密类型'
        });
      }

      // 映射前端类型到数据库类型
      const codeTypeMap = {
        'card': 'new_user',
        'renewal': 'renewal',
        'trial': 'new_user'
      };
      const codeType = codeTypeMap[type] || 'new_user';

      const generatedCodes = [];
      const finalBatchId = batchId || `BATCH_${Date.now()}`;
      const finalExpiresAt = expiresAt ? new Date(expiresAt) : null;

      // 批量生成卡密
      for (let i = 0; i < count; i++) {
        const code = generateActivationCode(durationDays);
        generatedCodes.push({
          code,
          code_type: codeType,
          duration_days: durationDays,
          max_uses: maxUses || 1,
          notes: description || `${type}类型卡密，有效期${durationDays}天`,
          batch_id: finalBatchId,
          created_by: req.user.userId || 'admin',
          expires_at: finalExpiresAt
        });
      }

      // 批量插入数据库
      for (const codeObj of generatedCodes) {
        await localPool.execute(
          `INSERT INTO activation_codes
           (code, code_type, duration_days, max_uses, notes, batch_id, created_by, expires_at)
           VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            codeObj.code, codeObj.code_type, codeObj.duration_days, codeObj.max_uses,
            codeObj.notes, codeObj.batch_id, codeObj.created_by, codeObj.expires_at
          ]
        );
      }

      console.log(`成功生成${count}个卡密，批次ID: ${finalBatchId}`);

      res.json({
        success: true,
        message: `成功生成${count}个卡密`,
        data: {
          codes: generatedCodes.map(c => c.code),
          batchId: finalBatchId,
          count: count,
          type: type,
          durationDays: durationDays,
          maxUses: maxUses
        }
      });

    } catch (error) {
      console.error('生成卡密失败:', error);
      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  });

  // 获取卡密列表API
  app.get('/api/admin/activation-codes', authenticateToken, requireAdmin, async (req, res) => {
    try {
      const {
        page = 1,
        limit = 20,
        type = '',
        status = '',
        batchId = '',
        search = ''
      } = req.query;



      let whereConditions = [];
      let params = [];

      if (type) {
        whereConditions.push('type = ?');
        params.push(type);
      }

      if (status) {
        whereConditions.push('status = ?');
        params.push(status);
      }

      if (batchId) {
        whereConditions.push('batch_id = ?');
        params.push(batchId);
      }

      if (search) {
        whereConditions.push('(code LIKE ? OR description LIKE ?)');
        params.push(`%${search}%`, `%${search}%`);
      }

      const whereClause = whereConditions.length > 0 ? 
        `WHERE ${whereConditions.join(' AND ')}` : '';

      const offset = (page - 1) * limit;

      // 获取总数
      const [countResult] = await localPool.execute(`
        SELECT COUNT(*) as total FROM activation_codes ${whereClause}
      `, params);

      // 获取列表数据
      const [codes] = await localPool.execute(`
        SELECT ac.*
        FROM activation_codes ac
        ${whereClause}
        ORDER BY ac.created_at DESC
        LIMIT ? OFFSET ?
      `, [...params, parseInt(limit), parseInt(offset)]);

      res.json({
        success: true,
        data: {
          codes: codes.map(code => ({
            ...code,
            isExpired: code.expires_at ? new Date() > new Date(code.expires_at) : false,
            isAvailable: code.status === 'active' && 
                        (code.expires_at ? new Date() <= new Date(code.expires_at) : true) &&
                        code.used_count < code.max_uses
          })),
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total: countResult[0].total,
            pages: Math.ceil(countResult[0].total / limit)
          }
        }
      });

    } catch (error) {
      console.error('获取卡密列表失败:', error);
      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  });

  // 获取卡密统计信息API
  app.get('/api/admin/activation-codes/stats', authenticateToken, requireAdmin, async (req, res) => {
    try {

      // 获取基础统计
      const [basicStats] = await localPool.execute(`
        SELECT
          COUNT(*) as total_codes,
          COUNT(CASE
            WHEN status = 'active'
            AND (expires_at IS NULL OR expires_at > NOW())
            AND used_count < max_uses
            THEN 1
          END) as active_codes,
          COUNT(CASE
            WHEN status = 'used'
            OR used_count >= max_uses
            THEN 1
          END) as used_codes,
          COUNT(CASE
            WHEN status = 'expired'
            OR (expires_at IS NOT NULL AND expires_at <= NOW())
            THEN 1
          END) as expired_codes,
          SUM(used_count) as total_uses,
          AVG(duration_days) as avg_duration
        FROM activation_codes
      `);

      // 获取类型统计
      const [typeStats] = await localPool.execute(`
        SELECT
          code_type as type,
          COUNT(*) as count,
          COUNT(CASE
            WHEN status = 'active'
            AND (expires_at IS NULL OR expires_at > NOW())
            AND used_count < max_uses
            THEN 1
          END) as active_count,
          SUM(used_count) as total_uses
        FROM activation_codes
        GROUP BY code_type
      `);

      // 获取批次统计
      const [batchStats] = await localPool.execute(`
        SELECT
          batch_id,
          COUNT(*) as count,
          COUNT(CASE
            WHEN status = 'active'
            AND (expires_at IS NULL OR expires_at > NOW())
            AND used_count < max_uses
            THEN 1
          END) as active_count,
          SUM(used_count) as total_uses,
          MAX(created_at) as latest_created
        FROM activation_codes
        WHERE batch_id IS NOT NULL
        GROUP BY batch_id
        ORDER BY latest_created DESC
        LIMIT 10
      `);

      // 获取最近使用记录
      const [recentUses] = await localPool.execute(`
        SELECT
          la.activation_code,
          la.created_at as activated_at,
          la.duration_days,
          u.username
        FROM local_activations la
        LEFT JOIN users u ON la.user_id = u.id
        ORDER BY la.created_at DESC
        LIMIT 10
      `);

      res.json({
        success: true,
        data: {
          basic: basicStats[0],
          byType: typeStats,
          byBatch: batchStats,
          recentUses: recentUses
        }
      });

    } catch (error) {
      console.error('获取卡密统计失败:', error);
      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  });

  // 更新卡密状态API
  app.put('/api/admin/activation-codes/:id/status', authenticateToken, requireAdmin, async (req, res) => {
    try {
      const { id } = req.params;
      const { status } = req.body;

      if (!['active', 'disabled', 'expired'].includes(status)) {
        return res.status(400).json({
          success: false,
          message: '无效的状态值'
        });
      }



      await localPool.execute(
        'UPDATE activation_codes SET status = ? WHERE id = ?',
        [status, id]
      );

      console.log(`管理员更新卡密状态: ID ${id} -> ${status}`);

      res.json({
        success: true,
        message: '卡密状态更新成功'
      });

    } catch (error) {
      console.error('更新卡密状态失败:', error);
      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  });

  // 删除卡密API
  app.delete('/api/admin/activation-codes/:id', authenticateToken, requireAdmin, async (req, res) => {
    try {
      const { id } = req.params;



      // 检查卡密是否已被使用
      const [codes] = await localPool.execute(
        'SELECT used_count FROM activation_codes WHERE id = ?',
        [id]
      );

      if (codes.length === 0) {
        return res.status(404).json({
          success: false,
          message: '卡密不存在'
        });
      }

      if (codes[0].used_count > 0) {
        return res.status(400).json({
          success: false,
          message: '已使用的卡密不能删除'
        });
      }

      await localPool.execute('DELETE FROM activation_codes WHERE id = ?', [id]);

      console.log(`管理员删除卡密: ID ${id}`);

      res.json({
        success: true,
        message: '卡密删除成功'
      });

    } catch (error) {
      console.error('删除卡密失败:', error);
      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  });

  console.log('✅ 管理员卡密管理模块设置完成');
}

module.exports = { setupAdminActivationCodes };
