"use strict";(self["webpackChunkautojs_web_control"]=self["webpackChunkautojs_web_control"]||[]).push([[159],{8159:function(t,e,a){a.r(e),a.d(e,{default:function(){return d}});var s=function(){var t=this,e=t._self._c;return e("div",{staticClass:"user-management-container"},[e("el-card",{staticClass:"page-header",attrs:{shadow:"never"}},[e("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e("span",{staticClass:"page-title"},[t._v("用户管理")]),e("el-button",{staticStyle:{float:"right",padding:"3px 0"},attrs:{type:"text"},on:{click:t.refreshData}},[t._v(" 刷新 ")])],1),e("div",{staticClass:"stats-row"},[e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:4}},[e("div",{staticClass:"stat-item"},[e("div",{staticClass:"stat-value"},[t._v(t._s(t.stats.users?.total_users||0))]),e("div",{staticClass:"stat-label"},[t._v("总用户数")])])]),e("el-col",{attrs:{span:4}},[e("div",{staticClass:"stat-item"},[e("div",{staticClass:"stat-value"},[t._v(t._s(t.stats.users?.active_users||0))]),e("div",{staticClass:"stat-label"},[t._v("活跃用户")])])]),e("el-col",{attrs:{span:4}},[e("div",{staticClass:"stat-item"},[e("div",{staticClass:"stat-value"},[t._v(t._s(t.stats.users?.expired_users||0))]),e("div",{staticClass:"stat-label"},[t._v("过期用户")])])]),e("el-col",{attrs:{span:4}},[e("div",{staticClass:"stat-item"},[e("div",{staticClass:"stat-value"},[t._v(t._s(t.stats.devices?.total_devices||0))]),e("div",{staticClass:"stat-label"},[t._v("总设备数")])])]),e("el-col",{attrs:{span:4}},[e("div",{staticClass:"stat-item"},[e("div",{staticClass:"stat-value"},[t._v(t._s(t.stats.devices?.online_devices||0))]),e("div",{staticClass:"stat-label"},[t._v("在线设备")])])]),e("el-col",{attrs:{span:4}},[e("div",{staticClass:"stat-item"},[e("div",{staticClass:"stat-value"},[t._v(t._s((t.stats.executions?.total_xiaohongshu_executions||0)+(t.stats.executions?.total_xianyu_executions||0)))]),e("div",{staticClass:"stat-label"},[t._v("总执行次数")])])])],1)],1)]),e("el-card",{staticClass:"list-card",attrs:{shadow:"never"}},[e("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e("span",[t._v("用户列表")]),e("div",{staticStyle:{float:"right"}},[e("el-input",{staticStyle:{width:"200px","margin-right":"10px"},attrs:{placeholder:"搜索用户名或邮箱","prefix-icon":"el-icon-search"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.loadUsers.apply(null,arguments)}},model:{value:t.searchForm.search,callback:function(e){t.$set(t.searchForm,"search",e)},expression:"searchForm.search"}}),e("el-select",{staticStyle:{width:"120px","margin-right":"10px"},attrs:{placeholder:"状态"},on:{change:t.loadUsers},model:{value:t.searchForm.status,callback:function(e){t.$set(t.searchForm,"status",e)},expression:"searchForm.status"}},[e("el-option",{attrs:{label:"全部",value:""}}),e("el-option",{attrs:{label:"活跃",value:"active"}}),e("el-option",{attrs:{label:"过期",value:"expired"}}),e("el-option",{attrs:{label:"禁用",value:"disabled"}})],1),e("el-select",{staticStyle:{width:"120px","margin-right":"10px"},attrs:{placeholder:"角色"},on:{change:t.loadUsers},model:{value:t.searchForm.role,callback:function(e){t.$set(t.searchForm,"role",e)},expression:"searchForm.role"}},[e("el-option",{attrs:{label:"全部",value:""}}),e("el-option",{attrs:{label:"用户",value:"user"}}),e("el-option",{attrs:{label:"管理员",value:"admin"}})],1),e("el-button",{attrs:{type:"primary"},on:{click:t.loadUsers}},[t._v("搜索")])],1)]),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:t.users,"default-sort":{prop:"created_at",order:"descending"}}},[e("el-table-column",{attrs:{prop:"username",label:"用户名",width:"120"}}),e("el-table-column",{attrs:{prop:"email",label:"邮箱",width:"180","show-overflow-tooltip":""}}),e("el-table-column",{attrs:{prop:"role",label:"角色",width:"80"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-tag",{attrs:{type:"admin"===a.row.role?"danger":"primary",size:"small"}},[t._v(" "+t._s("admin"===a.row.role?"管理员":"用户")+" ")])]}}])}),e("el-table-column",{attrs:{label:"账号状态",width:"100"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-tag",{attrs:{type:t.getAccountStatusType(a.row),size:"small"}},[t._v(" "+t._s(t.getAccountStatusText(a.row))+" ")])]}}])}),e("el-table-column",{attrs:{label:"时效信息",width:"150"},scopedSlots:t._u([{key:"default",fn:function(a){return[a.row.expires_at?e("div",[e("div",{staticStyle:{"font-size":"12px"}},[t._v(" "+t._s(t.formatDate(a.row.expires_at))+" ")]),e("div",{style:{color:a.row.isExpired?"#f56c6c":a.row.daysRemaining<=7?"#e6a23c":"#67c23a"}},[t._v(" "+t._s(a.row.isExpired?"已过期":`剩余${a.row.daysRemaining}天`)+" ")])]):e("span",[t._v("无限制")])]}}])}),e("el-table-column",{attrs:{label:"设备统计",width:"120"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("div",{staticStyle:{"font-size":"12px"}},[e("div",[t._v("总数: "+t._s(a.row.deviceStats?.total_devices||0))]),e("div",[t._v("在线: "+t._s(a.row.deviceStats?.online_devices||0))]),e("div",[t._v("忙碌: "+t._s(a.row.deviceStats?.busy_devices||0))])])]}}])}),e("el-table-column",{attrs:{label:"执行统计",width:"120"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("div",{staticStyle:{"font-size":"12px"}},[e("div",[t._v("小红书: "+t._s(a.row.execStats?.xiaohongshu_executions||0))]),e("div",[t._v("闲鱼: "+t._s(a.row.execStats?.xianyu_executions||0))]),e("div",[t._v("总计: "+t._s(a.row.execStats?.total_executions||0))])])]}}])}),e("el-table-column",{attrs:{label:"文件统计",width:"120"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("div",{staticStyle:{"font-size":"12px"}},[e("div",[t._v("文件数: "+t._s(a.row.fileStats?.total_files||0))]),e("div",[t._v("大小: "+t._s(t.formatFileSize(a.row.fileStats?.total_file_size||0)))])])]}}])}),e("el-table-column",{attrs:{prop:"created_at",label:"注册时间",width:"150"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t.formatDate(e.row.created_at))+" ")]}}])}),e("el-table-column",{attrs:{prop:"last_login_time",label:"最后登录",width:"150"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.last_login_time?t.formatDate(e.row.last_login_time):"从未登录")+" ")]}}])}),e("el-table-column",{attrs:{label:"操作",width:"200",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{attrs:{size:"mini"},on:{click:function(e){return t.viewUserDetail(a.row)}}},[t._v("详情")]),e("el-dropdown",{attrs:{trigger:"click"},on:{command:t.handleUserAction}},[e("el-button",{attrs:{size:"mini",type:"primary"}},[t._v(" 管理"),e("i",{staticClass:"el-icon-arrow-down el-icon--right"})]),e("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[e("el-dropdown-item",{attrs:{command:{action:"toggleStatus",user:a.row}}},[t._v(" "+t._s("active"===a.row.account_status?"禁用账号":"启用账号")+" ")]),"admin"!==a.row.role?e("el-dropdown-item",{attrs:{command:{action:"toggleRole",user:a.row}}},[t._v(" 设为管理员 ")]):t._e(),e("el-dropdown-item",{attrs:{command:{action:"extend",user:a.row}}},[t._v(" 延长时效 ")]),"admin"!==a.row.role?e("el-dropdown-item",{attrs:{command:{action:"delete",user:a.row}}},[e("span",{staticStyle:{color:"#f56c6c"}},[t._v("删除用户")])]):t._e()],1)],1)]}}])})],1),e("div",{staticClass:"pagination-container"},[e("el-pagination",{attrs:{"current-page":t.pagination.page,"page-sizes":[10,20,50,100],"page-size":t.pagination.limit,layout:"total, sizes, prev, pager, next, jumper",total:t.pagination.total},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)],1),e("el-dialog",{attrs:{title:"用户详情",visible:t.showUserDetail,width:"80%","close-on-click-modal":!1},on:{"update:visible":function(e){t.showUserDetail=e}}},[t.selectedUser?e("div",[e("el-tabs",{model:{value:t.activeTab,callback:function(e){t.activeTab=e},expression:"activeTab"}},[e("el-tab-pane",{attrs:{label:"基本信息",name:"basic"}},[e("el-descriptions",{attrs:{column:2,border:""}},[e("el-descriptions-item",{attrs:{label:"用户名"}},[t._v(t._s(t.selectedUser.user?.username))]),e("el-descriptions-item",{attrs:{label:"邮箱"}},[t._v(t._s(t.selectedUser.user?.email||"未设置"))]),e("el-descriptions-item",{attrs:{label:"角色"}},[e("el-tag",{attrs:{type:"admin"===t.selectedUser.user?.role?"danger":"primary"}},[t._v(" "+t._s("admin"===t.selectedUser.user?.role?"管理员":"用户")+" ")])],1),e("el-descriptions-item",{attrs:{label:"账号状态"}},[e("el-tag",{attrs:{type:t.getAccountStatusType(t.selectedUser.user)}},[t._v(" "+t._s(t.getAccountStatusText(t.selectedUser.user))+" ")])],1),e("el-descriptions-item",{attrs:{label:"注册时间"}},[t._v(t._s(t.formatDate(t.selectedUser.user?.created_at)))]),e("el-descriptions-item",{attrs:{label:"最后登录"}},[t._v(t._s(t.selectedUser.user?.last_login_time?t.formatDate(t.selectedUser.user?.last_login_time):"从未登录"))]),e("el-descriptions-item",{attrs:{label:"登录次数"}},[t._v(t._s(t.selectedUser.user?.login_count||0))]),e("el-descriptions-item",{attrs:{label:"激活次数"}},[t._v(t._s(t.selectedUser.user?.activation_count||0))]),e("el-descriptions-item",{attrs:{label:"账号过期时间"}},[t._v(t._s(t.selectedUser.user?.expires_at?t.formatDate(t.selectedUser.user?.expires_at):"无限制"))]),e("el-descriptions-item",{attrs:{label:"总激活天数"}},[t._v(t._s(t.selectedUser.user?.total_duration_days||0)+"天")])],1)],1),e("el-tab-pane",{attrs:{label:"设备列表",name:"devices"}},[e("el-table",{staticStyle:{width:"100%"},attrs:{data:t.selectedUser.devices}},[e("el-table-column",{attrs:{prop:"device_name",label:"设备名称"}}),e("el-table-column",{attrs:{prop:"device_id",label:"设备ID"}}),e("el-table-column",{attrs:{prop:"status",label:"状态"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-tag",{attrs:{type:"online"===a.row.status?"success":"busy"===a.row.status?"warning":"info"}},[t._v(" "+t._s(a.row.status)+" ")])]}}],null,!1,2217460297)}),e("el-table-column",{attrs:{prop:"last_seen",label:"最后活动"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t.formatDate(e.row.last_seen))+" ")]}}],null,!1,1062621219)})],1)],1),e("el-tab-pane",{attrs:{label:"激活记录",name:"activations"}},[e("el-table",{staticStyle:{width:"100%"},attrs:{data:t.selectedUser.activations}},[e("el-table-column",{attrs:{prop:"activation_code",label:"激活码"}}),e("el-table-column",{attrs:{prop:"activation_type",label:"类型"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-tag",{attrs:{size:"small"}},[t._v(" "+t._s("first_login"===a.row.activation_type?"首次登录":"renewal"===a.row.activation_type?"续期":"试用")+" ")])]}}],null,!1,2737446684)}),e("el-table-column",{attrs:{prop:"duration_days",label:"激活天数"}}),e("el-table-column",{attrs:{prop:"activated_at",label:"激活时间"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t.formatDate(e.row.activated_at))+" ")]}}],null,!1,4116453052)}),e("el-table-column",{attrs:{prop:"expires_at",label:"过期时间"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t.formatDate(e.row.expires_at))+" ")]}}],null,!1,992764737)})],1)],1),e("el-tab-pane",{attrs:{label:"执行记录",name:"executions"}},[e("div",{staticStyle:{"margin-bottom":"10px"}},[e("el-button",{attrs:{type:"danger",size:"small",loading:t.clearingLogs},on:{click:function(e){return t.clearUserExecutionLogs("all")}}},[t._v(" 清空所有记录 ")]),e("el-button",{attrs:{type:"warning",size:"small",loading:t.clearingLogs},on:{click:function(e){return t.clearUserExecutionLogs("xiaohongshu")}}},[t._v(" 清空小红书记录 ")]),e("el-button",{attrs:{type:"warning",size:"small",loading:t.clearingLogs},on:{click:function(e){return t.clearUserExecutionLogs("xianyu")}}},[t._v(" 清空闲鱼记录 ")])],1),e("el-table",{staticStyle:{width:"100%"},attrs:{data:t.selectedUser.recentExecutions}},[e("el-table-column",{attrs:{prop:"platform",label:"平台"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-tag",{attrs:{type:"xiaohongshu"===a.row.platform?"danger":"warning"}},[t._v(" "+t._s("xiaohongshu"===a.row.platform?"小红书":"闲鱼")+" ")])]}}],null,!1,2049140796)}),e("el-table-column",{attrs:{prop:"function_type",label:"功能类型"}}),e("el-table-column",{attrs:{prop:"execution_status",label:"执行状态"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-tag",{attrs:{type:"completed"===a.row.execution_status?"success":"failed"===a.row.execution_status?"danger":"warning"}},[t._v(" "+t._s(a.row.execution_status)+" ")])]}}],null,!1,3703817741)}),e("el-table-column",{attrs:{prop:"device_id",label:"设备ID"}}),e("el-table-column",{attrs:{prop:"started_at",label:"开始时间"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t.formatDate(e.row.started_at))+" ")]}}],null,!1,1970063040)})],1)],1),e("el-tab-pane",{attrs:{label:"文件列表",name:"files"}},[e("el-table",{staticStyle:{width:"100%"},attrs:{data:t.selectedUser.recentFiles}},[e("el-table-column",{attrs:{prop:"filename",label:"文件名"}}),e("el-table-column",{attrs:{prop:"file_type",label:"文件类型"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-tag",{attrs:{type:"uid"===a.row.file_type?"primary":"success"}},[t._v(" "+t._s("uid"===a.row.file_type?"UID文件":"视频文件")+" ")])]}}],null,!1,263523048)}),e("el-table-column",{attrs:{prop:"file_size",label:"文件大小"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t.formatFileSize(e.row.file_size))+" ")]}}],null,!1,2042086784)}),e("el-table-column",{attrs:{prop:"upload_time",label:"上传时间"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t.formatDate(e.row.upload_time))+" ")]}}],null,!1,3918146338)}),e("el-table-column",{attrs:{label:"操作",width:"100"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{attrs:{type:"danger",size:"mini",loading:t.deletingFile},on:{click:function(e){return t.deleteUserFile(a.row)}}},[t._v(" 删除 ")])]}}],null,!1,3330959367)})],1)],1)],1)],1):t._e(),e("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.showUserDetail=!1}}},[t._v("关闭")])],1)]),e("el-dialog",{attrs:{title:"延长用户时效",visible:t.showExtendDialog,width:"400px","close-on-click-modal":!1},on:{"update:visible":function(e){t.showExtendDialog=e}}},[e("el-form",{ref:"extendForm",attrs:{model:t.extendForm,rules:t.extendRules,"label-width":"100px"}},[e("el-form-item",{attrs:{label:"用户名"}},[e("span",[t._v(t._s(t.extendUser?.username))])]),e("el-form-item",{attrs:{label:"当前过期时间"}},[e("span",[t._v(t._s(t.extendUser?.expires_at?t.formatDate(t.extendUser.expires_at):"无限制"))])]),e("el-form-item",{attrs:{label:"延长天数",prop:"days"}},[e("el-input-number",{staticStyle:{width:"100%"},attrs:{min:1,max:3650},model:{value:t.extendForm.days,callback:function(e){t.$set(t.extendForm,"days",e)},expression:"extendForm.days"}})],1)],1),e("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.showExtendDialog=!1}}},[t._v("取消")]),e("el-button",{attrs:{type:"primary",loading:t.extending},on:{click:t.confirmExtend}},[t._v("确定")])],1)],1)],1)},l=[],r=a(4335),i={name:"UserManagement",data(){return{loading:!1,extending:!1,users:[],stats:{},showUserDetail:!1,showExtendDialog:!1,selectedUser:null,extendUser:null,activeTab:"basic",searchForm:{search:"",status:"",role:""},pagination:{page:1,limit:20,total:0},extendForm:{days:30},extendRules:{days:[{required:!0,message:"请输入延长天数",trigger:"blur"},{type:"number",min:1,max:3650,message:"天数必须在1-3650之间",trigger:"blur"}]},clearingLogs:!1,deletingFile:!1}},mounted(){this.loadStats(),this.loadUsers()},methods:{async loadStats(){try{const t=await r.A.get("/api/admin/users/stats",{headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}});t.data.success&&(this.stats=t.data.data)}catch(t){console.error("加载统计信息失败:",t)}},async loadUsers(){try{this.loading=!0;const t={page:this.pagination.page,limit:this.pagination.limit,...this.searchForm},e=await r.A.get("/api/admin/users",{params:t,headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}});e.data.success&&(this.users=e.data.data.users,this.pagination=e.data.data.pagination)}catch(t){console.error("加载用户列表失败:",t),this.$message.error("加载用户列表失败")}finally{this.loading=!1}},async viewUserDetail(t){try{const e=await r.A.get(`/api/admin/users/${t.id}`,{headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}});e.data.success&&(this.selectedUser=e.data.data,this.showUserDetail=!0,this.activeTab="basic")}catch(e){console.error("加载用户详情失败:",e),this.$message.error("加载用户详情失败")}},async handleUserAction(t){const{action:e,user:a}=t;switch(e){case"toggleStatus":await this.toggleUserStatus(a);break;case"toggleRole":await this.toggleUserRole(a);break;case"extend":this.showExtendUserDialog(a);break;case"delete":await this.deleteUser(a);break}},async toggleUserStatus(t){try{const e="active"===t.account_status?"disabled":"active";await this.$confirm(`确定要${"active"===e?"启用":"禁用"}用户 ${t.username} 吗？`,"确认操作",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await r.A.put(`/api/admin/users/${t.id}/status`,{status:e},{headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}}),this.$message.success("用户状态更新成功"),this.loadUsers(),this.loadStats()}catch(e){"cancel"!==e&&(console.error("更新用户状态失败:",e),this.$message.error("更新用户状态失败"))}},async toggleUserRole(t){try{await this.$confirm(`确定要将用户 ${t.username} 设为管理员吗？`,"确认操作",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await r.A.put(`/api/admin/users/${t.id}/role`,{role:"admin"},{headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}}),this.$message.success("用户角色更新成功"),this.loadUsers()}catch(e){"cancel"!==e&&(console.error("更新用户角色失败:",e),this.$message.error("更新用户角色失败"))}},showExtendUserDialog(t){this.extendUser=t,this.extendForm.days=30,this.showExtendDialog=!0},async confirmExtend(){try{await this.$refs.extendForm.validate(),this.extending=!0,await r.A.put(`/api/admin/users/${this.extendUser.id}/extend`,this.extendForm,{headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}}),this.$message.success("用户时效延长成功"),this.showExtendDialog=!1,this.loadUsers(),this.loadStats()}catch(t){console.error("延长用户时效失败:",t),this.$message.error(t.response?.data?.message||"延长用户时效失败")}finally{this.extending=!1}},async deleteUser(t){try{await this.$confirm(`确定要删除用户 ${t.username} 吗？删除后无法恢复，该用户的所有数据都将被删除。`,"确认删除",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"error"}),await r.A.delete(`/api/admin/users/${t.id}`,{headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}}),this.$message.success("用户删除成功"),this.loadUsers(),this.loadStats()}catch(e){"cancel"!==e&&(console.error("删除用户失败:",e),this.$message.error(e.response?.data?.message||"删除用户失败"))}},refreshData(){this.loadStats(),this.loadUsers()},handleSizeChange(t){this.pagination.limit=t,this.pagination.page=1,this.loadUsers()},handleCurrentChange(t){this.pagination.page=t,this.loadUsers()},getAccountStatusType(t){return t?t.isExpired||"expired"===t.account_status?"danger":"active"===t.account_status?"success":"disabled"===t.account_status?"warning":"info":"info"},getAccountStatusText(t){return t?t.isExpired?"已过期":"active"===t.account_status?"正常":"disabled"===t.account_status?"已禁用":"expired"===t.account_status?"已过期":t.account_status:"未知"},formatDate(t){return t?new Date(t).toLocaleString("zh-CN"):""},formatFileSize(t){if(!t)return"0 B";const e=1024,a=["B","KB","MB","GB"],s=Math.floor(Math.log(t)/Math.log(e));return parseFloat((t/Math.pow(e,s)).toFixed(2))+" "+a[s]},async deleteUserFile(t){try{await this.$confirm(`确定要删除文件 ${t.filename} 吗？`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),this.deletingFile=!0,await r.A.delete(`/api/admin/users/${this.selectedUser.user.id}/files/${t.id}?fileType=${t.file_type}`,{headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}}),this.$message.success("文件删除成功"),await this.viewUserDetail(this.selectedUser.user)}catch(e){"cancel"!==e&&(console.error("删除文件失败:",e),this.$message.error("删除文件失败"))}finally{this.deletingFile=!1}},async clearUserExecutionLogs(t){try{const e="all"===t?"所有":"xiaohongshu"===t?"小红书":"闲鱼";await this.$confirm(`确定要清空用户的${e}执行记录吗？`,"确认清空",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),this.clearingLogs=!0,await r.A.delete(`/api/admin/users/${this.selectedUser.user.id}/execution-logs?platform=${t}`,{headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}}),this.$message.success("执行记录清空成功"),await this.viewUserDetail(this.selectedUser.user)}catch(e){"cancel"!==e&&(console.error("清空执行记录失败:",e),this.$message.error("清空执行记录失败"))}finally{this.clearingLogs=!1}}}},o=i,n=a(1656),c=(0,n.A)(o,s,l,!1,null,"f1af2a36",null),d=c.exports}}]);